// 页面初始化配置
export const PAGINATION = {
  current: 1,
  pageSize: 20,
  total: 0,
  seriesTotal: 0,
  showQuickJumper: false,
  showSizeChanger: true,
  pageSizeOptions: [10, 20, 50, 100, 200],
  showTotal: (total: number) => `共 ${total} 条记录`,
}

export const DEFAULT_PAGINATION = { current: 1, pageSize: 10 }

// 价格货币枚举
export enum PriceCurrency {
  RMB = 1, // 人民币
  USD = 2, // 美元
  JPY = 3, // 日元
}

// 价格货币配置（统一管理标签和颜色）
export const PRICE_CURRENCY_CONFIG: Record<number, { label: string; color: string }> = {
  [PriceCurrency.RMB]: { label: '人民币 (¥)', color: 'green' },
  [PriceCurrency.USD]: { label: '美元 ($)', color: 'blue' },
  [PriceCurrency.JPY]: { label: '日元 (Ұ)', color: 'orange' },
} as const

// 基于配置生成价格货币选项数组（用于Select组件）
export const PRICE_CURRENCY_OPTIONS = Object.entries(PRICE_CURRENCY_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 项目类型枚举
export enum ProductionType {
  SELF_PRODUCED = 0, // 自制
  COMMISSIONED = 1, // 承制
  COMMISSIONED_FUZHOU = 2, // 承制（福州地接）
}

// 项目类型配置（统一管理标签和颜色）
export const PRODUCTION_TYPE_CONFIG: Record<ProductionType, { label: string; color: string }> = {
  [ProductionType.SELF_PRODUCED]: { label: '自制', color: 'blue' },
  [ProductionType.COMMISSIONED]: { label: '承制', color: 'green' },
  [ProductionType.COMMISSIONED_FUZHOU]: { label: '承制（福州地接）', color: 'orange' },
} as const

// 基于配置生成项目类型选项数组（用于Select组件）
export const PRODUCTION_TYPE_OPTIONS = Object.entries(PRODUCTION_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as ProductionType,
  color: config.color,
}))

// 性别配置（统一管理标签和颜色）
export const GENDER_CONFIG: Record<number, { label: string; color: string; disabled?: boolean }> = {
  1: { label: '男', color: 'blue' },
  2: { label: '女', color: 'pink' },
  3: { label: '保密', color: 'purple' },
} as const

// 基于配置生成性别选项数组（用于Select组件）
export const GENDER_OPTIONS = Object.entries(GENDER_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 性别要求
export const GENDER_MAP: Record<number, { label: string }> = {
  1: { label: '男' },
  2: { label: '女' },
  3: { label: '不限' },
} as const

export const GENDERS = Object.entries(GENDER_MAP).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
}))

// 薪资类型配置（统一管理标签和颜色）
export const SALARY_TYPE_CONFIG: Record<number, { label: string; color: string }> = {
  1: { label: '单价', color: 'blue' },
  2: { label: '总价', color: 'green' },
} as const

// 基于配置生成薪资类型选项数组（用于Select组件）
export const SALARY_TYPE_OPTIONS = Object.entries(SALARY_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 招募人员类型配置（一级类型）
export const RECRUITMENT_PARENT_TYPE_CONFIG: Record<number, { label: string; color: string }> = {
  1: { label: '剧组', color: 'blue' },
  2: { label: '演员', color: 'green' },
} as const

// 基于配置生成招募人员类型选项数组（用于Select组件）
export const RECRUITMENT_PARENT_TYPE_OPTIONS = Object.entries(RECRUITMENT_PARENT_TYPE_CONFIG).map(
  ([value, config]) => ({
    label: config.label,
    value: Number(value),
    color: config.color,
  })
)

// 是否内部配置（统一管理标签和颜色）
export const IS_INTERNAL_CONFIG: Record<number, { label: string; color: string }> = {
  0: { label: '外部', color: 'default' },
  1: { label: '内部', color: 'green' },
} as const

// 基于配置生成是否内部选项数组（用于Select组件）
export const IS_INTERNAL_OPTIONS = Object.entries(IS_INTERNAL_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 是否微信用户配置（统一管理标签和颜色）
export const IS_WX_USER_CONFIG: Record<number, { label: string; color: string }> = {
  0: { label: '后台', color: 'default' },
  1: { label: '微信', color: 'cyan' },
} as const

// 基于配置生成是否微信用户选项数组（用于Select组件）
export const IS_WX_USER_OPTIONS = Object.entries(IS_WX_USER_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 人员类型枚举
export enum PersonType {
  Individual = 1, // 个人
  Group = 2, // 团体
}

// 人员类型配置（统一管理标签和颜色）
export const PERSON_TYPE_CONFIG: Record<number, { label: string; color: string }> = {
  [PersonType.Individual]: { label: '个人', color: 'blue' },
  [PersonType.Group]: { label: '团体', color: 'green' },
} as const

// 基于配置生成人员类型选项数组（用于Select组件）
export const PERSON_TYPE_OPTIONS = Object.entries(PERSON_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 合同状态枚举
export const enum ContractStatusEnum {
  PendingEditorReview = 0, // 待责编审核
  WaitingForSign = 1, // 等待签章
  SignCompleted = 2, // 签署完成
  ReviewRejected = 3, // 审核拒绝
  OAApproving = 4, // OA审批中
  OAApproved = 5, // OA通过
  OARejected = 6, // OA拒绝
  CompanySigning = 11, // 公司签署中
  SealCompleted = 12, // 签章完成
  Archived = 100, // 已归档
  Voided = 101, // 已作废
}

// 合同状态配置
export const CONTRACT_STATUS_CONFIG: any = {
  [ContractStatusEnum.PendingEditorReview]: { label: '新建', color: 'default' },
  [ContractStatusEnum.WaitingForSign]: { label: '等待签章', color: 'processing' },
  [ContractStatusEnum.SignCompleted]: { label: '签署完成', color: 'processing' },
  [ContractStatusEnum.ReviewRejected]: { label: '审核拒绝', color: 'error' },
  [ContractStatusEnum.OAApproving]: { label: 'OA审批中', color: 'processing' },
  [ContractStatusEnum.OAApproved]: { label: 'OA通过', color: 'success' },
  [ContractStatusEnum.OARejected]: { label: 'OA拒绝', color: 'error' },
  [ContractStatusEnum.CompanySigning]: { label: '公司签署中', color: 'processing' },
  [ContractStatusEnum.SealCompleted]: { label: '签约完成', color: 'success' },
  [ContractStatusEnum.Archived]: { label: '已归档', color: 'success' },
  [ContractStatusEnum.Voided]: { label: '已作废', color: 'error' },
} as const

// 合同类型配置（统一管理标签）
export const CONTRACT_TYPE_CONFIG: Record<number, string> = {
  101: '保密协议',
  102: '演员合同',
  103: '劳务合同',
  104: '演员合同(含超时)',
  105: '补充协议(演职)',
  199: '其他合同',
} as const

// 基于配置生成合同类型选项数组（用于Select组件）
export const CONTRACT_TYPE_OPTIONS = Object.entries(CONTRACT_TYPE_CONFIG).map(([value, label]) => ({
  label,
  value: Number(value),
}))

export const PAGE_STATE = {
  ADD: 'add',
  EDIT: 'edit',
  LOG: 'log',
  COPY: 'copy',
  PATCH_EDIT: 'patch_edit',
  PATCH_DELETE: 'patch_delete',
  REFUND: 'refund',
}

export const ACTION = {
  EDIT: 'edit',
  DELETE: 'delete',
  SET: 'set',
  LOG: 'log',
  STATUS: 'status',
  REFUND: 'refund',
  ECHO: 'echo',
  CALLBACK: 'callback',
  DISABLED: 'disabled',
  ENABLED: 'enabled',
  COPY: 'copy',
  RESET: 'reset',
  RESET_PASSWORD: 'reset_password',
  RESET_PASSWORD_SUCCESS: 'reset_password_success',
  RESET_PASSWORD_FAIL: 'reset_password_fail',
  RESET_PASSWORD_ERROR: 'reset_password_error',
}

export type IPageState = (typeof PAGE_STATE)[keyof typeof PAGE_STATE]

export type IIndexState = {
  current: number
  pageSize: number
  pageState: boolean | IPageState
  detailData: any
  activeBookId?: number
  total: number
  selectedRows?: any[]
  [key: string]: any
}

export const getPagination = (state: IIndexState, setState: any) => {
  const { current, pageSize } = state

  return {
    current,
    pageSize,
    size: 'large',
    showTotal: () => null,
    onChange: (page: number, size: number) => {
      setState({ current: page, pageSize: size })
    },
    showSizeChanger: true, // 显示分页大小选择器
    hideOnSinglePage: false,
  }
}

export const SAYINGS = [
  '世界上只有一种真正的英雄主义，那就是在看清生活的真相之后，依然热爱生活。',
  '既往不恋，纵情向前。',
  '凡是过往，皆为序章。',
  '如果一帆风顺太难，那我祝你乘风破浪。',
  '星光不问赶路人，岁月不负有心人。',
  '即使明天是世界末日，今夜我也要在园中种满莲花。',
  '只要热爱生命，一切都在意料之中。',
  '问题如果有办法解决，你就不必担心；如果没有办法解决，你担心也没有用。',
  '有的人居无定所地过着安定的日子，有的人却在豪华住宅里一辈子逃亡。',
  '十年以后你会相信吗？路过春秋都写在身上。',
  '当星宿都沉没山岳，只盼你会抬头，看我寄托的弯月。',
  '昨天太近，明天太远。',
  '晚风吻尽荷花叶，任我醉倒在池边。',
  '这城市每个角落，回忆都霸占街头。',
  '只要心还透明，就能折射希望。',
  '每个渺小的理由，都困住自由。',
  '游客是你，风景是我，无法避免，让你经过。',
  '长亭外，古道边，芳草碧连天。',
  '天空和我的中间，只剩倾盆的思念。',
  '下段旅程，你一定要更幸福丰盛。',
  '飞过人间的无常，才懂爱才是宝藏。',
  '七岁的那一年，抓住那只蝉，以为能抓住夏天。',
  '生命是华丽错觉，时间是贼，偷走一切。',
  '得不到的永远在骚动，被偏爱的都有恃无恐。',
  '我不知道将去何方，但我已在路上。',
  '只要今天比昨天做的好，这就是希望。',
  '当坚冰还盖着北海的时候，我看到了怒放的梅花。',
  '缓缓飘落的枫叶像思念，我点燃烛火温暖岁末的秋天。',
  '如果再也见不到你，祝你早安，午安，晚安。',
  '人生就像一盒巧克力，你永远不知道下一颗是什么滋味。',
  '希望是件美丽的东西，也许是最好的东西。',
  '短短的一生，我们最终都会失去，你不妨大胆一些。爱一个人，攀一座山，追一个梦。',
  '谁画出这天地， 又画下我和你，让我们的世界绚丽多彩。',
  '我祈祷拥有一颗透明的心灵和会流泪的眼睛。',
  '风到这里就是粘，粘住过客的思念。',
  '青春是挽不回的水，转眼消失在指间，用力的浪费，再用力的后悔。',
  '天青色等烟雨，而我在等你。',
  '最美的不是下雨天，是曾与你躲过雨的屋檐。',
  '风筝在阴天搁浅，想念还在等待救援，我拉着线复习你给的温柔。',
  '苍天笑，纷纷世上潮，谁负谁胜出天知晓。',
  '江山笑，烟雨遥，涛浪淘尽红尘俗世知多少。',
  '当陪你的人要下车时，即使不舍，也该心存感激，然后挥手道别。',
  '当天你离我而赶路，一转眼回头便苍老。',
  '有生之年能遇见你，竟花光我所有运气。',
  '尘埃落定之后，回忆别来挑拨。',
  '原来我非不快乐，只我一人未发觉。',
  '短暂的总是浪漫，漫长总会不满。',
  '太早遇见太晚发现，辜负了命运的经营。',
  '我看见，每天的夕阳也会有变化。',
  '想得不可得，你奈人生何。',
  '我曾经跨过山和大海，也穿过人山人海。我曾经拥有着的一切，转眼都飘散如烟。我曾经失落失望，失掉所有方向，直到看见平凡 才是唯一的答案。',
  '曾梦想仗剑走天涯，看一看世界的繁华。',
]

export const ENV_OPTIONS = [
  { value: 'dev', label: 'dev' },

  /*
   * { value: 'dev1', label: 'dev1' },
   * { value: 'dev2', label: 'dev2' },
   * { value: 'dev3', label: 'dev3' },
   */
  { value: 'test', label: 'test' },

  /*
   * { value: 'test1', label: 'test1' },
   * { value: 'test2', label: 'test2' },
   * { value: 'test3', label: 'test3' },
   * { value: 'test5', label: 'test5' },
   * { value: 'test6', label: 'test6' },
   */
]

// 媒体类型常量映射
export const MEDIA_TYPE_OPTIONS = [
  {
    value: 2,
    label: '自我介绍',
    component: 'video',
    icon: 'VideoCameraOutlined',
    color: 'purple',
    tags: ['mediaUrl'],
    multiple: false,
    maxCount: 1,
    maxSize: 100,
  },
  {
    value: 1,
    label: '近照',
    component: 'image',
    icon: 'PictureOutlined',
    color: 'blue',
    tags: ['mediaUrl'],
    multiple: true,
    maxCount: 5,
    maxSize: 20,
  },
  {
    value: 3,
    label: '剧照',
    component: 'image',
    icon: 'PictureOutlined',
    color: 'blue',
    tags: ['mediaUrl'],
    multiple: true,
    maxCount: 10,
    maxSize: 20,
  },
  {
    value: 5,
    label: '简历',
    component: 'file',
    icon: 'FileOutlined',
    color: 'green',
    multiple: false,
    maxCount: 1,
    maxSize: 100,
  },
  {
    value: 4,
    label: '最新报价',
    component: 'inputNumber',
    icon: 'DollarOutlined',
    color: 'orange',
    unit: '元',
    placeholder: '请输入报价金额',
  },
  {
    value: 6,
    label: '作品片段',
    component: 'video',
    icon: 'VideoCameraOutlined',
    color: 'purple',
    multiple: true,
    maxCount: 3,
    maxSize: 200,
  },
  {
    value: 7,
    label: '荣誉榜',
    component: 'image',
    icon: 'PictureOutlined',
    color: 'blue',
    multiple: true,
    maxCount: 8,
    maxSize: 20,
  },
  {
    value: 8,
    label: '才艺展示',
    component: 'video',
    icon: 'VideoCameraOutlined',
    color: 'purple',
    multiple: true,
    maxCount: 5,
    maxSize: 150,
  },

  /*
   * {
   *   value: 9,
   *   label: '其他',
   *   component: 'file',
   *   icon: 'FileOutlined',
   *   color: 'green',
   *   multiple: true,
   *   maxCount: 10,
   *   maxSize: 50,
   * },
   */
]

// 作品类型选项
export const WORK_TYPE_OPTIONS = [
  { label: '武侠', value: '武侠' },
  { label: '现代', value: '现代' },
  { label: '古装', value: '古装' },
  { label: '都市', value: '都市' },
  { label: '科幻', value: '科幻' },
  { label: '悬疑', value: '悬疑' },
  { label: '喜剧', value: '喜剧' },
  { label: '爱情', value: '爱情' },
  { label: '其他', value: '其他' },
]

// 人员角色类型枚举（包含演员）
export enum RoleType {
  Director = 1, // 导演
  Screenwriter = 2, // 编剧
  Producer = 3, // 制片人
  Photographer = 4, // 摄影
  Lighting = 5, // 灯光
  Costume = 6, // 服装
  Props = 7, // 道具
  Makeup = 8, // 化妆
  Actor = 9, // 演员
  ExecutiveProducer = 10, // 总监制
  ResponsibleProducer = 11, // 负责监制
  LineProducer = 12, // 执行制片
  ActorCoordinator = 13, // 演员统筹
  AssistantDirectorActor = 14, // 演员副导
  Coordinator = 15, // 统筹
  ExecutiveDirector = 16, // 执行导演
  DIT = 17, // DIT
  FieldProducer = 18, // 现场制片
  Driver = 19, // 司机
  ArtDirector = 20, // 美术
  CostumeAndMakeupHead = 21, // 服化负责人
  ProductionManager = 22, // 制片主任
  ExternalProducer = 23, // 外联制片
  LifeProducer = 24, // 生活制片
  SetAssistant = 25, // 场务
  ScriptSupervisor = 26, // 场记
  PhotographyAssistant = 27, // 摄影助理
  StillPhotographer = 28, // 剧照
  SoundRecordist = 29, // 收音师
  SoundAssistant = 30, // 收音助理
  LightingTechnician = 31, // 灯光师
  LightingAssistant = 32, // 灯光师助理
  Stylist = 33, // 造型师
  OnSetSupervisor = 34, // 现场主盯
  OnSetMakeup = 35, // 现场跟妆
  MakeupArtist = 36, // 改妆师
  HairAndMakeup = 37, // 梳化
  SetConstruction = 38, // 制景
  CameraEquipment = 39, // 摄影器材
  LightingEquipment = 40, // 灯光器材
  SetSupplies = 41, // 场务用品
  MartialArtsDirector = 42, // 武术指导
  Wuxing = 43, // 武行
  ArtAssistant = 44, // 美术助理
  StylistAssistant = 45, // 造型师助理
  CostumeAssistant = 46, // 服装助理
  DirectorAssistant = 47, // 导演助理
  ProducerAssistant = 48, // 制片助理
  MakeupAssistant = 49, // 化妆助理
  ActorAssistant = 50, // 演员助理
  Cleaner = 51 // 保洁人员
}

// 演员角色类型枚举
export enum ActorRoleType {
  LeadingActress = 1, // 女一
  SecondActress = 2, // 女二
  SupportingActress = 3, // 女配
  LeadingActor = 4, // 男一
  SecondActor = 5, // 男二
  SupportingActor = 6, // 男配
  SupportingRole = 7, // 配角
  GuestRole = 8, // 客串
  Extra = 9, // 群演
  SpecialGuest = 10, // 特邀演员
  ThirdActress = 11, // 女三
  FourthActress = 12, // 女四
  FifthActress = 13, // 女五
  ThirdActor = 14, // 男三
  FourthActor = 15, // 男四
  FifthActor = 16, // 男五
  FemaleAntagonist = 17, // 女反
  MaleAntagonist = 18, // 男反
  FollowingActor = 19, // 跟组演员
}

// 性别枚举
export enum Gender {
  MALE = 1, // 男
  FEMALE = 2, // 女
  // OTHER = 3, // bao
  UNDISCLOSED = 4, // 保密
}

// 是否内部枚举
export enum IsInternal {
  EXTERNAL = 0, // 外部
  INTERNAL = 1, // 内部
}

// 人员状态枚举
export enum PersonStatus {
  Normal = 1, // 正常
  Disabled = 2, // 禁用
}

// 人员状态配置（统一管理标签和颜色）
export const PERSON_STATUS_CONFIG: Record<PersonStatus, { label: string; color: string }> = {
  [PersonStatus.Normal]: { label: '合作中', color: 'success' },
  [PersonStatus.Disabled]: { label: '禁止合作', color: 'error' },
} as const

// 基于配置生成人员状态选项数组（用于Select组件）
export const PERSON_STATUS_OPTIONS = Object.entries(PERSON_STATUS_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as PersonStatus,
  color: config.color,
}))

// 人员角色类型配置（包含演员）
export const ROLE_TYPE_CONFIG: Record<RoleType, { label: string; color: string; sort: number; disable?: boolean }> = {
  [RoleType.Director]: { label: '导演', color: 'red', sort: 1 },
  [RoleType.DirectorAssistant]: { label: '导演助理', color: 'geekblue', sort: 2 },
  [RoleType.ExecutiveDirector]: { label: '执行导演', color: 'orange', sort: 3 },
  [RoleType.AssistantDirectorActor]: { label: '演员副导', color: 'red', sort: 4 },
  [RoleType.Screenwriter]: { label: '编剧', color: 'blue', sort: 10 },
  [RoleType.ExecutiveProducer]: { label: '总监制', color: 'magenta', sort: 20 },
  [RoleType.ResponsibleProducer]: { label: '负责监制', color: 'gold', sort: 21 },
  [RoleType.Producer]: { label: '制片人', color: 'green', sort: 22 },
  [RoleType.LineProducer]: { label: '执行制片', color: 'lime', sort: 23 },
  [RoleType.ProducerAssistant]: { label: '制片助理', color: 'lime', sort: 24 },
  [RoleType.ProductionManager]: { label: '制片主任', color: 'gold', sort: 25 },
  [RoleType.ExternalProducer]: { label: '外联制片', color: 'blue', sort: 26 },
  [RoleType.LifeProducer]: { label: '生活制片', color: 'green', sort: 27 },
  [RoleType.FieldProducer]: { label: '现场制片', color: 'cyan', sort: 28 },
  [RoleType.Photographer]: { label: '摄影', color: 'orange', sort: 30 },
  [RoleType.PhotographyAssistant]: { label: '摄影助理', color: 'orange', sort: 31 },
  [RoleType.StillPhotographer]: { label: '剧照', color: 'blue', sort: 32 },
  [RoleType.DIT]: { label: 'DIT', color: 'purple', sort: 33 },
  [RoleType.CameraEquipment]: { label: '摄影器材', color: 'orange', sort: 34 },
  [RoleType.Lighting]: { label: '灯光', color: 'purple', sort: 40 },
  [RoleType.LightingTechnician]: { label: '灯光师', color: 'purple', sort: 41 },
  [RoleType.LightingAssistant]: { label: '灯光助理', color: 'purple', sort: 42 },
  [RoleType.LightingEquipment]: { label: '灯光器材', color: 'purple', sort: 43 },
  [RoleType.ArtDirector]: { label: '美术', color: 'magenta', sort: 50 },
  [RoleType.ArtAssistant]: { label: '美术助理', color: 'geekblue', sort: 51 },
  [RoleType.SetConstruction]: { label: '制景', color: 'magenta', sort: 52 },
  [RoleType.Costume]: { label: '服装', color: 'pink', sort: 60 },
  [RoleType.CostumeAssistant]: { label: '服装助理', color: 'pink', sort: 61 },
  [RoleType.CostumeAndMakeupHead]: { label: '服化负责人', color: 'volcano', sort: 62 },
  [RoleType.Makeup]: { label: '化妆', color: 'volcano', sort: 70 },
  [RoleType.MakeupAssistant]: { label: '化妆助理', color: 'volcano', sort: 71 },
  [RoleType.Stylist]: { label: '造型师', color: 'pink', sort: 72 },
  [RoleType.StylistAssistant]: { label: '造型师助理', color: 'pink', sort: 73 },
  [RoleType.OnSetMakeup]: { label: '现场跟妆', color: 'volcano', sort: 74 },
  [RoleType.MakeupArtist]: { label: '改妆师', color: 'volcano', sort: 75 },
  [RoleType.HairAndMakeup]: { label: '梳化', color: 'volcano', sort: 76 },
  [RoleType.Props]: { label: '道具', color: 'cyan', sort: 80 },
  [RoleType.SoundRecordist]: { label: '收音师', color: 'purple', sort: 90 },
  [RoleType.SoundAssistant]: { label: '收音助理', color: 'purple', sort: 91 },
  [RoleType.Actor]: { label: '演员', color: 'geekblue', sort: 100, disable: true },
  [RoleType.ActorAssistant]: { label: '演员助理', color: 'orange', sort: 102 },
  [RoleType.ActorCoordinator]: { label: '演员统筹', color: 'blue', sort: 101 },
  [RoleType.MartialArtsDirector]: { label: '武术指导', color: 'magenta', sort: 110 },
  [RoleType.Wuxing]: { label: '武行', color: 'volcano', sort: 111 },
  [RoleType.Coordinator]: { label: '统筹', color: 'green', sort: 120 },
  [RoleType.SetAssistant]: { label: '场务', color: 'orange', sort: 121 },
  [RoleType.ScriptSupervisor]: { label: '场记', color: 'cyan', sort: 122 },
  [RoleType.SetSupplies]: { label: '场务用品', color: 'orange', sort: 123 },
  [RoleType.OnSetSupervisor]: { label: '现场主盯', color: 'red', sort: 124 },
  [RoleType.Driver]: { label: '司机', color: 'geekblue', sort: 130 },
  [RoleType.Cleaner]: { label: '保洁人员', color: 'cyan', sort: 131, disable: true },
}

// 演员角色类型配置（统一管理标签和颜色）
export const ACTOR_ROLE_TYPE_CONFIG: Record<string, { label: string; color: string; isAbstract?: boolean }> = {
  [ActorRoleType.LeadingActress]: { label: '女一', color: 'red' },
  [ActorRoleType.SecondActress]: { label: '女二', color: 'volcano' },
  [ActorRoleType.SupportingActress]: { label: '女配', color: 'pink' },
  [ActorRoleType.LeadingActor]: { label: '男一', color: 'blue' },
  [ActorRoleType.SecondActor]: { label: '男二', color: 'geekblue' },
  [ActorRoleType.SupportingActor]: { label: '男配', color: 'cyan' },
  [ActorRoleType.SupportingRole]: { label: '配角', color: 'green', isAbstract: false },
  [ActorRoleType.GuestRole]: { label: '客串', color: 'orange', isAbstract: false },
  [ActorRoleType.Extra]: { label: '群演', color: 'purple', isAbstract: true },
  [ActorRoleType.SpecialGuest]: { label: '特约', color: 'magenta', isAbstract: true },
  [ActorRoleType.ThirdActress]: { label: '女三', color: 'pink' },
  [ActorRoleType.FourthActress]: { label: '女四', color: 'pink' },
  [ActorRoleType.FifthActress]: { label: '女五', color: 'pink' },
  [ActorRoleType.ThirdActor]: { label: '男三', color: 'cyan' },
  [ActorRoleType.FourthActor]: { label: '男四', color: 'cyan' },
  [ActorRoleType.FifthActor]: { label: '男五', color: 'cyan' },
  [ActorRoleType.FemaleAntagonist]: { label: '女反', color: 'pink' },
  [ActorRoleType.MaleAntagonist]: { label: '男反', color: 'pink' },
  [ActorRoleType.FollowingActor]: { label: '跟组演员', color: 'pink', isAbstract: false },
} as const

// 基于配置生成角色类型选项数组（用于Select组件）
export const ROLE_TYPE_OPTIONS_SELECT = Object.entries(ROLE_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as RoleType,
  disabled: !!config?.disable,
}))

// 基于配置生成演员角色类型选项数组（用于Select组件）
export const ACTOR_ROLE_TYPE_OPTIONS = Object.entries(ACTOR_ROLE_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as ActorRoleType,
  color: config.color,
  isAbstract: !!config.isAbstract,
}))

// 实名验证结果枚举
export enum ValidRealNameResult {
  Incomplete = 0, // 信息不全
  Verified = 1, // 已实名
  Unverified = 2, // 未实名
}

// 实名认证状态枚举
export enum FddVerifyStatus {
  Inactive = 0, // 未激活
  Uncertified = 1, // 未认证
  Verified = 2, // 审核通过
  Pending = 3, // 已提交待审核
  Rejected = 4, // 审核不通过
  Incomplete = 5, // 信息不全
}

// 实名认证状态配置（统一管理标签和颜色）
export const FDD_VERIFY_STATUS_CONFIG: Record<number, { label: string; color: string }> = {
  [FddVerifyStatus.Inactive]: { label: '未激活', color: 'default' },
  [FddVerifyStatus.Uncertified]: { label: '未认证', color: 'warning' },
  [FddVerifyStatus.Verified]: { label: '审核通过', color: 'success' },
  [FddVerifyStatus.Pending]: { label: '审核中', color: 'processing' },
  [FddVerifyStatus.Rejected]: { label: '审核不通过', color: 'error' },
  [FddVerifyStatus.Incomplete]: { label: '信息不全', color: 'warning' },
} as const

// 人员申请状态配置（统一管理标签和颜色）
export const APPLICATION_STATUS_CONFIG: Record<number, { label: string; color: any }> = {
  1: { label: '待审核', color: 'processing' },
  2: { label: '已通过', color: 'success' },
  3: { label: '已拒绝', color: 'error' },
  4: { label: '已取消', color: 'default' },
} as const

// 基于配置生成人员申请状态选项数组（用于Select组件）
export const APPLICATION_STATUS_OPTIONS = Object.entries(APPLICATION_STATUS_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 基于配置生成实名认证状态选项数组（用于Select组件）
export const FDD_VERIFY_STATUS_OPTIONS = Object.entries(FDD_VERIFY_STATUS_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

export const isExtraFunc = (val: ActorRoleType) => [ActorRoleType.Extra, ActorRoleType.SpecialGuest].includes(val)

// 费用类型枚举
export enum ExpenseType {
  PosterMaterial = 1, // 海报物料
  PreparationCost = 2, // 筹备期费用
  VehicleRental = 3, // 车辆租赁
  FuelCost = 4, // 燃油费
  DramaVehicle = 5, // 戏用车辆
  OpeningCeremony = 6, // 开机
  SetConsumption = 7, // 场务消耗
  TravelExpense = 8, // 差旅费
  DramaMiscellaneous = 9, // 剧杂费
  InsuranceCost = 10, // 保险费
  MealCost = 11, // 餐费
  RiskControlBudget = 12, // 片场风险控制预算
  AccommodationCost = 15, // 住宿费
  AdvertisementProduction = 16, // 广告制作
  TollFee = 17, // 过路费
  ParkingFee = 18, // 停车费
  TransportationFee = 19, // 车费
  ToolVehicle = 20, // 工具车
  ProductionRental = 21, // 制片-租赁费用
  ProductionPurchase = 22, // 制片-采购费用
  ProductionConsumption = 23, // 制片-损耗费用
  PhotographyPurchase = 24, // 摄影-采购费用
  PhotographyRental = 25, // 摄影-租赁费用
  PhotographyConsumption = 26, // 摄影-损耗费用
  LightingRental = 27, // 灯光-租赁费用
  LightingPurchase = 28, // 灯光-采购费用
  LightingConsumption = 29, // 灯光-损耗费用
  SoundRental = 30, // 录音-租赁费用
  SoundPurchase = 31, // 录音-采购费用
  SoundConsumption = 32, // 录音-损耗费用
  ArtRental = 33, // 美术-租赁费用
  ArtPurchase = 34, // 美术-采购费用
  ArtConsumption = 35, // 美术-损耗费用
  CostumeRental = 36, // 服装-租赁费用
  CostumePurchase = 37, // 服装-采购费用
  CostumeConsumption = 38, // 服装-损耗费用
  MakeupRental = 39, // 化妆-租赁费用
  MakeupPurchase = 40, // 化妆-采购费用
  MakeupConsumption = 41, // 化妆-损耗费用
  PropsRental = 42, // 道具-租赁费用
  PropsPurchase = 43, // 道具-采购费用
  PropsConsumption = 44, // 道具-损耗费用
  DriverRental = 45, // 司机-租赁费用
  DriverPurchase = 46, // 司机-采购费用
  DriverConsumption = 47, // 司机-损耗费用
  PackageBudget = 48, // 打包费用
  PostProduction = 49, // 后期制作费
  VoiceProduction = 50, // 配音制作费
}

// 费用类型配置
export const EXPENSE_TYPE_CONFIG: Record<ExpenseType, { label: string; color: string; sort: number }> = {
  [ExpenseType.PosterMaterial]: { label: '设计物料', color: 'blue', sort: 1 },
  [ExpenseType.PreparationCost]: { label: '筹备期费用', color: 'green', sort: 2 },
  [ExpenseType.OpeningCeremony]: { label: '开机', color: 'cyan', sort: 3 },
  [ExpenseType.AdvertisementProduction]: { label: '广告制作', color: 'purple', sort: 4 },
  [ExpenseType.PostProduction]: { label: '后期制作费', color: 'purple', sort: 5 },
  [ExpenseType.VoiceProduction]: { label: '配音制作费', color: 'purple', sort: 6 },

  [ExpenseType.VehicleRental]: { label: '车辆租赁', color: 'orange', sort: 10 },
  [ExpenseType.FuelCost]: { label: '燃油费', color: 'red', sort: 11 },
  [ExpenseType.DramaVehicle]: { label: '戏用车辆', color: 'purple', sort: 12 },
  [ExpenseType.TollFee]: { label: '过路费', color: 'orange', sort: 13 },
  [ExpenseType.ParkingFee]: { label: '停车费', color: 'pink', sort: 14 },
  [ExpenseType.TransportationFee]: { label: '车费', color: 'cyan', sort: 15 },
  [ExpenseType.ToolVehicle]: { label: '工具车', color: 'geekblue', sort: 16 },

  [ExpenseType.TravelExpense]: { label: '差旅费', color: 'magenta', sort: 20 },
  [ExpenseType.AccommodationCost]: { label: '住宿费', color: 'geekblue', sort: 21 },
  [ExpenseType.MealCost]: { label: '餐费', color: 'lime', sort: 22 },

  [ExpenseType.InsuranceCost]: { label: '保险费', color: 'gold', sort: 30 },
  [ExpenseType.RiskControlBudget]: { label: '片场风险控制预算', color: 'pink', sort: 31 },

  [ExpenseType.SetConsumption]: { label: '场务消耗', color: 'geekblue', sort: 40 },
  [ExpenseType.DramaMiscellaneous]: { label: '剧杂费', color: 'volcano', sort: 41 },

  [ExpenseType.ProductionRental]: { label: '制片-租赁费用', color: 'magenta', sort: 50 },
  [ExpenseType.ProductionPurchase]: { label: '制片-采购费用', color: 'gold', sort: 51 },
  [ExpenseType.ProductionConsumption]: { label: '制片-损耗费用', color: 'volcano', sort: 52 },

  [ExpenseType.PhotographyRental]: { label: '摄影-租赁费用', color: 'cyan', sort: 60 },
  [ExpenseType.PhotographyPurchase]: { label: '摄影-采购费用', color: 'purple', sort: 61 },
  [ExpenseType.PhotographyConsumption]: { label: '摄影-损耗费用', color: 'geekblue', sort: 62 },

  [ExpenseType.LightingRental]: { label: '灯光-租赁费用', color: 'magenta', sort: 70 },
  [ExpenseType.LightingPurchase]: { label: '灯光-采购费用', color: 'gold', sort: 71 },
  [ExpenseType.LightingConsumption]: { label: '灯光-损耗费用', color: 'volcano', sort: 72 },

  [ExpenseType.SoundRental]: { label: '录音-租赁费用', color: 'purple', sort: 80 },
  [ExpenseType.SoundPurchase]: { label: '录音-采购费用', color: 'cyan', sort: 81 },
  [ExpenseType.SoundConsumption]: { label: '录音-损耗费用', color: 'geekblue', sort: 82 },

  [ExpenseType.ArtRental]: { label: '美术-租赁费用', color: 'magenta', sort: 90 },
  [ExpenseType.ArtPurchase]: { label: '美术-采购费用', color: 'gold', sort: 91 },
  [ExpenseType.ArtConsumption]: { label: '美术-损耗费用', color: 'volcano', sort: 92 },

  [ExpenseType.CostumeRental]: { label: '服装-租赁费用', color: 'purple', sort: 100 },
  [ExpenseType.CostumePurchase]: { label: '服装-采购费用', color: 'cyan', sort: 101 },
  [ExpenseType.CostumeConsumption]: { label: '服装-损耗费用', color: 'geekblue', sort: 102 },

  [ExpenseType.MakeupRental]: { label: '化妆-租赁费用', color: 'magenta', sort: 110 },
  [ExpenseType.MakeupPurchase]: { label: '化妆-采购费用', color: 'gold', sort: 111 },
  [ExpenseType.MakeupConsumption]: { label: '化妆-损耗费用', color: 'volcano', sort: 112 },

  [ExpenseType.PropsRental]: { label: '道具-租赁费用', color: 'purple', sort: 120 },
  [ExpenseType.PropsPurchase]: { label: '道具-采购费用', color: 'cyan', sort: 121 },
  [ExpenseType.PropsConsumption]: { label: '道具-损耗费用', color: 'geekblue', sort: 122 },

  [ExpenseType.DriverRental]: { label: '司机-租赁费用', color: 'magenta', sort: 130 },
  [ExpenseType.DriverPurchase]: { label: '司机-采购费用', color: 'gold', sort: 131 },
  [ExpenseType.DriverConsumption]: { label: '司机-损耗费用', color: 'volcano', sort: 132 },
  [ExpenseType.PackageBudget]: { label: '打包费用', color: 'purple', sort: 140 },
} as const

// 基于配置生成费用类型选项数组（用于Select组件）
export const EXPENSE_TYPE_OPTIONS = Object.entries(EXPENSE_TYPE_CONFIG)
  .map(([value, config]) => ({
    label: config.label,
    value: Number(value) as ExpenseType,
    color: config.color,
  }))
  .sort((a, b) => EXPENSE_TYPE_CONFIG[a.value].sort - EXPENSE_TYPE_CONFIG[b.value].sort)

// 场次标注内容类型枚举
export enum ContentType {
  SCENE = 1, // 场景
  ACTOR = 2, // 演员
  FEATURED_EXTRA = 3, // 特约/群特
  EXTRA = 4, // 群演
  COSTUME_PROP_CUE = 5, // 服化道提示
  // SCENE_DIVISION = 6, // 分场景
}

// 场次标注内容类型配置（统一管理标签和颜色）
export const CONTENT_TYPE_CONFIG: Record<ContentType, { label: string; color: string; sort: number }> = {
  [ContentType.SCENE]: { label: '场景', color: 'lime', sort: 1 },
  // [ContentType.SCENE_DIVISION]: { label: '分场景', color: 'lightblue', sort: 2 },
  [ContentType.ACTOR]: { label: '演员', color: 'orange', sort: 3 },
  [ContentType.FEATURED_EXTRA]: { label: '特约/群特', color: 'pink', sort: 4 },
  [ContentType.EXTRA]: { label: '群演', color: 'gold', sort: 5 },
  [ContentType.COSTUME_PROP_CUE]: { label: '服化道', color: 'cyan', sort: 6 },
} as const

// 基于配置生成场次标注内容类型选项数组（用于Select组件）
export const CONTENT_TYPE_OPTIONS = Object.entries(CONTENT_TYPE_CONFIG)
  .map(([value, config]) => ({
    label: config.label,
    value: Number(value) as ContentType,
    color: config.color,
  }))
  .sort((a, b) => CONTENT_TYPE_CONFIG[a.value].sort - CONTENT_TYPE_CONFIG[b.value].sort)

// 场次气氛枚举
export enum AtmosphereType {
  DAY = 1, // 日
  NIGHT = 2, // 夜
  DAY_TO_NIGHT = 3, // 日转夜
  NIGHT_TO_DAY = 4, // 夜转日
}

// 场次气氛配置（统一管理标签和颜色）
export const ATMOSPHERE_TYPE_CONFIG: Record<AtmosphereType, { label: string; color: string }> = {
  [AtmosphereType.DAY]: { label: '日', color: 'gold' },
  [AtmosphereType.NIGHT]: { label: '夜', color: 'purple' },
  [AtmosphereType.DAY_TO_NIGHT]: { label: '日转夜', color: 'volcano' },
  [AtmosphereType.NIGHT_TO_DAY]: { label: '夜转日', color: 'cyan' },
} as const

// 基于配置生成场次气氛选项数组（用于Select组件）
export const ATMOSPHERE_TYPE_OPTIONS = Object.entries(ATMOSPHERE_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as AtmosphereType,
  color: config.color,
}))

// 场次内外景枚举
export enum LocationType {
  INTERIOR = 1, // 内
  EXTERIOR = 2, // 外
}

// 场次内外景配置（统一管理标签和颜色）
export const LOCATION_TYPE_CONFIG: Record<LocationType, { label: string; color: string }> = {
  [LocationType.INTERIOR]: { label: '内', color: 'blue' },
  [LocationType.EXTERIOR]: { label: '外', color: 'green' },
} as const

// 基于配置生成场次内外景选项数组（用于Select组件）
export const LOCATION_TYPE_OPTIONS = Object.entries(LOCATION_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as LocationType,
  color: config.color,
}))

// 场次计划类型枚举
export enum PlanType {
  SCENE_DETAIL = 0, // 场次明细
  SCENE_DAY_DIVISION = 1, // 场次日分割
  SCENE_TRANSITION_DIVISION = 2, // 场次转场分割
}

// 场次计划类型配置（统一管理标签和颜色）
export const PLAN_TYPE_CONFIG: Record<PlanType, { label: string; color: string }> = {
  [PlanType.SCENE_DETAIL]: { label: '场次明细', color: 'blue' },
  [PlanType.SCENE_DAY_DIVISION]: { label: '场次日分割', color: 'green' },
  [PlanType.SCENE_TRANSITION_DIVISION]: { label: '场次转场分割', color: 'orange' },
} as const

// 基于配置生成场次计划类型选项数组（用于Select组件）
export const PLAN_TYPE_OPTIONS = Object.entries(PLAN_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as PlanType,
  color: config.color,
}))

// 房型枚举
export enum RoomType {
  SINGLE = 1, // 单床房
  DOUBLE = 2, // 双床房
  TRIPLE = 3, // 三床房
}

// 房型配置（统一管理标签和颜色）
export const ROOM_TYPE_CONFIG: Record<RoomType, { label: string; color: string }> = {
  [RoomType.SINGLE]: { label: '单床房', color: 'blue' },
  [RoomType.DOUBLE]: { label: '双床房', color: 'green' },
  [RoomType.TRIPLE]: { label: '三床房', color: 'orange' },
} as const

// 评价类型枚举
export enum EvaluationType {
  WORK = 0, // 工作评价
  ACCOMMODATION = 1, // 住宿评价
}

// 评价类型配置（统一管理标签和颜色）
export const EVALUATION_TYPE_CONFIG: Record<EvaluationType, { label: string; color: string }> = {
  [EvaluationType.WORK]: { label: '工作评价', color: 'blue' },
  [EvaluationType.ACCOMMODATION]: { label: '住宿评价', color: 'green' },
} as const

// 基于配置生成评价类型选项数组（用于Select组件）
export const EVALUATION_TYPE_OPTIONS = Object.entries(EVALUATION_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as EvaluationType,
  color: config.color,
}))

// 基于配置生成房型选项数组（用于Select组件）
export const ROOM_TYPE_OPTIONS = Object.entries(ROOM_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as RoomType,
  color: config.color,
}))

// 房间等级枚举
export enum RoomLevel {
  EXCELLENT = 1, // 优
  GOOD = 2, // 良
  AVERAGE = 3, // 一般
}

// 房间等级配置（统一管理标签和颜色）
export const ROOM_LEVEL_CONFIG: Record<RoomLevel, { label: string; color: string }> = {
  [RoomLevel.EXCELLENT]: { label: '优', color: 'success' },
  [RoomLevel.GOOD]: { label: '良', color: 'processing' },
  [RoomLevel.AVERAGE]: { label: '一般', color: 'warning' },
} as const

// 基于配置生成房间等级选项数组（用于Select组件）
export const ROOM_LEVEL_OPTIONS = Object.entries(ROOM_LEVEL_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as RoomLevel,
  color: config.color,
}))

// 房间方位选项
export const ROOM_POSITION_OPTIONS = [
  { label: '上', value: '上' },
  { label: '下', value: '下' },
]

// 房间状态配置（统一管理标签和颜色）
export const ROOM_STATUS_CONFIG: Record<number, { label: string; color: string }> = {
  1: { label: '启用', color: 'success' },
  0: { label: '禁用', color: 'error' },
} as const

// 基于配置生成房间状态选项数组（用于Select组件）
export const ROOM_STATUS_OPTIONS = Object.entries(ROOM_STATUS_CONFIG)
  .reverse()
  .map(([value, config]) => ({
    label: config.label,
    value: Number(value),
    color: config.color,
  }))

// 保洁状态枚举
export enum CleanStatus {
  PENDING = 0, // 待打扫
  CLEANING = 1, // 打扫中
  COMPLETED = 2, // 打扫完成
}

// 保洁状态配置（统一管理标签和颜色）
export const CLEAN_STATUS_CONFIG: Record<CleanStatus, { label: string; color: string }> = {
  [CleanStatus.PENDING]: { label: '待打扫', color: 'warning' },
  [CleanStatus.CLEANING]: { label: '打扫中', color: 'processing' },
  [CleanStatus.COMPLETED]: { label: '打扫完成', color: 'success' },
} as const

// 基于配置生成保洁状态选项数组（用于Select组件）
export const CLEAN_STATUS_OPTIONS = Object.entries(CLEAN_STATUS_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as CleanStatus,
  color: config.color,
}))

// 用餐类型枚举
export enum MealType {
  BREAKFAST = 1, // 早餐
  LUNCH = 2, // 午餐
  DINNER = 3, // 晚餐
}

// 用餐类型配置（统一管理标签和颜色）
export const MEAL_TYPE_CONFIG: Record<MealType, { label: string; color: string }> = {
  [MealType.BREAKFAST]: { label: '早餐', color: 'gold' },
  [MealType.LUNCH]: { label: '午餐', color: 'green' },
  [MealType.DINNER]: { label: '晚餐', color: 'blue' },
} as const

// 基于配置生成用餐类型选项数组（用于Select组件）
export const MEAL_TYPE_OPTIONS = Object.entries(MEAL_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as MealType,
  color: config.color,
}))

// 通告单演员角色类型枚举
export enum SceneCallActorRoleType {
  LEAD_ACTOR = 1, // 协领主演
  MAIN_ACTOR = 2, // 主演
  SPECIAL_GUEST = 3, // 特邀
}

// 通告单演员角色类型配置
export const SCENE_CALL_ACTOR_ROLE_TYPE_CONFIG: Record<SceneCallActorRoleType, { label: string; color: string }> = {
  [SceneCallActorRoleType.LEAD_ACTOR]: { label: '协领主演', color: 'red' },
  [SceneCallActorRoleType.MAIN_ACTOR]: { label: '主演', color: 'blue' },
  [SceneCallActorRoleType.SPECIAL_GUEST]: { label: '特邀', color: 'green' },
} as const

// 基于配置生成通告单演员角色类型选项数组（用于Select组件）
export const SCENE_CALL_ACTOR_ROLE_TYPE_OPTIONS = Object.entries(SCENE_CALL_ACTOR_ROLE_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as SceneCallActorRoleType,
  color: config.color,
}))

// 高德地图配置
export const AMAP_CONFIG = {
  // 高德地图 API Key
  KEY: 'f7947774e041ab7b119da38611d2080f', // 请替换为您的实际 Key
  // 高德地图安全密钥
  JSCODE: 'dd98134e12b475f2f94da7609d1ab3c4', // 请替换为您的实际安全密钥
} as const

// 摄像机状态枚举
export enum CameraStatus {
  PENDING = 0, // 待定
  OK = 1, // 通过
  NG = 2, // 不通过
}

// 摄像机状态配置（统一管理标签和颜色）
export const CAMERA_STATUS_CONFIG: Record<CameraStatus, { label: string; color: string }> = {
  [CameraStatus.PENDING]: { label: '待定', color: 'default' },
  [CameraStatus.OK]: { label: '通过', color: 'success' },
  [CameraStatus.NG]: { label: '不通过', color: 'error' },
} as const

// 基于配置生成摄像机状态选项数组（用于Select组件）
export const CAMERA_STATUS_OPTIONS = Object.entries(CAMERA_STATUS_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as CameraStatus,
  color: config.color,
}))

// 摄像机景别枚举
export enum CameraShot {
  CLOSE_UP = '特写',
  MEDIUM_CLOSE_UP = '近景',
  MEDIUM_SHOT = '中景',
  LONG_SHOT = '远景',
  FULL_SHOT = '全景',
  EMPTY_SHOT = '空镜',
  SMALL_FULL_SHOT = '小全',
}

// 摄像机景别配置（统一管理标签和颜色）
export const CAMERA_SHOT_CONFIG: Record<CameraShot, { label: string; color: string }> = {
  [CameraShot.CLOSE_UP]: { label: '特写', color: 'red' },
  [CameraShot.MEDIUM_CLOSE_UP]: { label: '近景', color: 'orange' },
  [CameraShot.MEDIUM_SHOT]: { label: '中景', color: 'blue' },
  [CameraShot.LONG_SHOT]: { label: '远景', color: 'green' },
  [CameraShot.FULL_SHOT]: { label: '全景', color: 'purple' },
  [CameraShot.EMPTY_SHOT]: { label: '空镜', color: 'cyan' },
  [CameraShot.SMALL_FULL_SHOT]: { label: '小全', color: 'geekblue' },
} as const

// 摄像机景别选项数组（用于Select组件）
export const CAMERA_SHOT_OPTIONS = [
  { label: '特写', value: CameraShot.CLOSE_UP },
  { label: '近景', value: CameraShot.MEDIUM_CLOSE_UP },
  { label: '中景', value: CameraShot.MEDIUM_SHOT },
  { label: '远景', value: CameraShot.LONG_SHOT },
  { label: '全景', value: CameraShot.FULL_SHOT },
  { label: '空镜', value: CameraShot.EMPTY_SHOT },
  { label: '小全', value: CameraShot.SMALL_FULL_SHOT },
] as const
