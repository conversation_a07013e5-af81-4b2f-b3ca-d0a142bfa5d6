import { ROLE_TYPE_CONFIG } from '@/consts'
import PersonSelector from '@/pages/production/person/components/PersonSelector'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Col, Drawer, Form, InputNumber, message, Radio, Row, Segmented, Select, Space, Tabs } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { IProductionListItem } from '../store'

interface IAddPersonDrawerProps {
  open: boolean
  onCancel: () => void
  onSuccess: (persons: any[]) => void
  loading?: boolean
  productionId: number
  project?: IProductionListItem
}

// 角色分组配置
const ROLE_GROUPS = [
  {
    key: 'director',
    label: '导演组',
    sortRange: [1, 10],
    description: '导演、编剧等创作人员'
  },
  {
    key: 'producer',
    label: '制片组',
    sortRange: [20, 29],
    description: '制片人、制片助理、统筹、场务等制片人员'
  },
  {
    key: 'photography',
    label: '摄影组',
    sortRange: [30, 39],
    description: '摄影师、摄影助理、剧照等'
  },
  {
    key: 'lighting',
    label: '灯光组',
    sortRange: [40, 49],
    description: '灯光师、灯光助理等'
  },
  {
    key: 'art',
    label: '美术组',
    sortRange: [50, 59],
    description: '美术、美术助理、制景等'
  },
  {
    key: 'costume',
    label: '服化道组',
    sortRange: [60, 89],
    description: '服装、化妆、造型、道具等'
  },
  {
    key: 'sound',
    label: '音响组',
    sortRange: [90, 99],
    description: '收音师、收音助理等'
  },
  {
    key: 'actor',
    label: '演员组',
    sortRange: [100, 119],
    description: '演员统筹、演员助理、武术指导等'
  },

]

// 根据分组生成人员列表初始值
const generateGroupedPersonList = () => {
  const groupedList: Record<string, any[]> = {}

  ROLE_GROUPS.forEach(group => {
    let groupRoles = Object.keys(ROLE_TYPE_CONFIG)
      .filter(item => {
        const roleType = parseInt(item, 10)
        const config = (ROLE_TYPE_CONFIG as any)[roleType]
        // 过滤掉禁用的角色
        if (config?.disable) return false

        // 制片组特殊处理：包含原制片组(20-28)和原现场组(120-130)
        if (group.key === 'producer') {
          return (config?.sort >= 20 && config?.sort <= 28) ||
                 (config?.sort >= 120 && config?.sort <= 130)
        }

        // 其他组按原范围过滤
        return config?.sort >= group.sortRange[0] &&
               config?.sort <= group.sortRange[1]
      })
      .sort((a, b) => (ROLE_TYPE_CONFIG as any)[a].sort - (ROLE_TYPE_CONFIG as any)[b].sort)
      .map(roleType => ({
        roleType: parseInt(roleType, 10),
        hasInvoice: false,
        personCount: 0,
        dayCount: 1,
      }))

    groupedList[group.key] = groupRoles
  })

  return groupedList
}

const AddPersonDrawer: React.FC<IAddPersonDrawerProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  project,
}) => {
  const [form] = Form.useForm()
  const [activeTab, setActiveTab] = useState('director')

  // 使用 useMemo 缓存分组数据
  const groupedPersonList = useMemo(() => generateGroupedPersonList(), [])



  useEffect(() => {
    if (open) {
      form.resetFields()
      setActiveTab('director') // 重置到第一个 tab
      setTimeout(() => {
        // 初始化完整的人员列表
        form.setFieldsValue({ personList: groupedPersonList?.['director'] || [] })
      }, 50)
    } else {
      form.setFieldsValue({ personList: [] })
    }
  }, [open])

  // 处理 Tab 切换
  const handleTabChange = (e: any) => {
    if (e?.target?.value) {
      setActiveTab(e.target.value)
      form.setFieldsValue({ personList: groupedPersonList?.[e.target.value] || [] })
    }
  }


  // 处理人员选择
  const handlePersonSelect = (personId: number, option: any, name: number) => {
    if (personId) {
      form.setFieldValue(['personList', name, 'personName'], option.personName)
      form.setFieldValue(['personList', name, 'isInternal'], option.isInternal)
      form.setFieldValue(['personList', name, 'personCount'], 1)
    }
  }

  // 处理批量新增人员
  const handleAddPersons = (values: any) => {
    try {
      const { personList } = values

      // 将多选数组拆开平铺，为每个人员创建记录
      const flattenedPersonList: any[] = []

      personList.forEach((roleItem: any) => {
        const {
          personId,
          roleType,
          quotedPrice,
          personName,
          isInternal,
          personCount,
          hasInvoice,
          description,
          dayCount,
          totalPrice,
        } = roleItem

        // if (personId || (quotedPrice && personCount)) {
        if (personId || quotedPrice || totalPrice) {
          flattenedPersonList.push({
            personId: personId || 0,
            roleType,
            personName,
            quotedPrice,
            isInternal,
            personCount,
            hasInvoice,
            description,
            dayCount,
            totalPrice,
          })
        }
      })

      if (flattenedPersonList.length === 0) {
        message.warning('请至少选择一位人员')

        return
      }

      // 提取所有人员ID用于重复性验证
      const newPersonIds = flattenedPersonList.filter(item => !!item.personId).map(item => item.personId)
      const uniquePersonIds = [...new Set(newPersonIds)]

      if (uniquePersonIds.length !== newPersonIds.length) {
        message.warning('不能重复选择同一位人员')

        return
      }

      onSuccess(flattenedPersonList)
    } catch (error) {
      console.error('添加人员失败:', error)
    }
  }

  return (
    <Drawer
      title="添加项目人员"
      open={open}
      onClose={onCancel}
      width={1200}
      destroyOnHidden
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" onClick={() => form.submit()} loading={loading}>
              立即添加
            </Button>
          </Space>
        </div>
      }>
      <Radio.Group onChange={handleTabChange} value={activeTab} style={{ marginBottom: 8 }}>
        {ROLE_GROUPS.map(item => (
          <Radio.Button value={item.key}>{item.label}</Radio.Button>
        ))}
      </Radio.Group>
      <Form
        form={form}
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        layout="horizontal"
        onFinish={handleAddPersons}
        initialValues={{ personList: [] }}>
        <Form.List name="personList">
          {(fields, { add, remove }) => {
            const formPersonList = form.getFieldValue('personList') || []

            return (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <div key={key}>
                    <Row>
                      <Col span={5}>
                        <Form.Item
                          {...restField}
                          name={[name, 'personId']}
                          label={(ROLE_TYPE_CONFIG as any)?.[formPersonList?.[name]?.roleType]?.label || ''}
                          labelCol={{ span: 8 }}
                          wrapperCol={{ span: 16 }}>
                          <PersonSelector
                            key={`${key}_${formPersonList?.[name]?.roleType}`}
                            roleType={formPersonList?.[name]?.roleType}
                            onSelect={(val, opt) => handlePersonSelect(val, opt, name)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'personCount']}
                          label="人数"
                          labelCol={{ span: 10 }}
                          initialValue={1}>
                          <InputNumber placeholder="人数" min={0} precision={0} className="full-h" />
                        </Form.Item>
                      </Col>

                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'dayCount']}
                          label="天数"
                          labelCol={{ span: 10 }}
                          initialValue={1}>
                          <InputNumber placeholder="天数" min={1} precision={0} className="full-h" />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item label="单价" {...restField} name={[name, 'quotedPrice']} labelCol={{ span: 10 }}>
                          <InputNumber
                            placeholder="单价"
                            min={0}
                            precision={2}
                            prefix={project?.currencySymbol || '¥'}
                            className="full-h"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item {...restField} name={[name, 'totalPrice']} label="总价" labelCol={{ span: 10 }}>
                          <InputNumber
                            placeholder="总价"
                            min={0}
                            precision={2}
                            prefix={project?.currencySymbol || '¥'}
                            className="full-h"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item
                          {...restField}
                          name={[name, 'hasInvoice']}
                          label="发票"
                          labelCol={{ span: 10 }}
                          initialValue={false}>
                          <Select
                            placeholder="请选择"
                            options={[
                              { value: true, label: '是' },
                              { value: false, label: '否' },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={2}>
                        <Space.Compact style={{ marginLeft: '10px' }}>
                          <Button
                            type="default"
                            size="small"
                            shape="round"
                            onClick={() => add({ roleType: formPersonList?.[name]?.roleType }, name + 1)}>
                            <PlusOutlined />
                          </Button>
                          {fields.length > 1 && (
                            <Button type="default" size="small" shape="round" onClick={() => remove(name)}>
                              <DeleteOutlined />
                            </Button>
                          )}
                        </Space.Compact>
                      </Col>
                    </Row>

                    {/* 隐藏字段存储人员信息 */}
                    <Form.Item {...restField} name={[name, 'personName']} hidden></Form.Item>
                    <Form.Item {...restField} name={[name, 'isInternal']} hidden></Form.Item>
                    <Form.Item {...restField} name={[name, 'roleType']} hidden></Form.Item>
                  </div>
                ))}
              </>
            )
          }}
        </Form.List>
      </Form>
    </Drawer>
  )
}

export default AddPersonDrawer
