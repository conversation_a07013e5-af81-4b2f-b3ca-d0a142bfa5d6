import { Collapse, Empty, Input, Space, Spin, Table, Tag, Typography, message } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { ATMOSPHERE_TYPE_CONFIG, CAMERA_SHOT_CONFIG, CAMERA_STATUS_CONFIG, LOCATION_TYPE_CONFIG } from '@/consts'
import useProjectStore, { IPrSceneCallInfo } from '../../store'
import { IProductionListItem } from '../../list/store'
import { Dict } from '@fe/rockrose'
import { useDebounceFn } from 'ahooks'

interface FilmShotLogTabProps {
  productionId: number
  project: IProductionListItem
}

const FilmShotLogTab: React.FC<FilmShotLogTabProps> = ({ productionId, project }) => {
  const { getSceneAndFilmShotLogs } = useProjectStore()
  const [loading, setLoading] = useState(false)
  const [sceneData, setSceneData] = useState<IPrSceneCallInfo[]>([])
  const [searchText, setSearchText] = useState('')

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const result = await getSceneAndFilmShotLogs(productionId)
      setSceneData(result)
    } catch (error) {
      console.error('获取场记数据失败:', error)
      message.error('获取场记数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [productionId])

  // 搜索防抖
  const { run: onSearchDebounce } = useDebounceFn((value: string) => {
    setSearchText(value)
  }, { wait: 300 })

  // 过滤后的场次数据
  const filteredSceneData = useMemo(() => {
    if (!searchText.trim()) {
      return sceneData
    }

    const searchLower = searchText.toLowerCase()
    return sceneData.filter(scene =>
      scene.sceneNumber?.toLowerCase().includes(searchLower) ||
      scene.shootingLocation?.toLowerCase().includes(searchLower) ||
      scene.scene?.toLowerCase().includes(searchLower) ||
      scene.mainContent?.toLowerCase().includes(searchLower) ||
      scene.mainActors?.toLowerCase().includes(searchLower) ||
      scene.groupExtraActors?.toLowerCase().includes(searchLower) ||
      scene.specialActors?.toLowerCase().includes(searchLower) ||
      scene.remark?.toLowerCase().includes(searchLower)
    )
  }, [sceneData, searchText])



  // 场记日志表格列配置
  const filmShotLogColumns = [
    {
      title: '场次编号',
      dataIndex: 'sceneNumber',
      key: 'sceneNumber',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '镜号',
      dataIndex: 'shotNumber',
      key: 'shotNumber',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '次数',
      dataIndex: 'takeNumber',
      key: 'takeNumber',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '氛围描述',
      dataIndex: 'atmosphere',
      key: 'atmosphere',
      width: 120,
      ellipsis: true,
      align: 'center' as const,
    },
    {
      title: 'A机景别',
      dataIndex: 'cameraShotByA',
      key: 'cameraShotByA',
      width: 100,
      align: 'center' as const,
      render: (value: string) => {
        const config = CAMERA_SHOT_CONFIG[value as keyof typeof CAMERA_SHOT_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : value || '-'
      },
    },
        {
      title: 'A机状态',
      dataIndex: 'cameraStatusByA',
      key: 'cameraStatusByA',
      width: 100,
      align: 'center' as const,
      render: (value: number) => {
        const config = CAMERA_STATUS_CONFIG[value as keyof typeof CAMERA_STATUS_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : '-'
      },
    },
      {
      title: 'A机文件号',
      dataIndex: 'cameraFileByA',
      key: 'cameraFileByA',
      width: 100,
      align: 'center' as const,
      render: (value: string) => value || '-',
    },
    {
      title: 'B机景别',
      dataIndex: 'cameraShotByB',
      key: 'cameraShotByB',
      width: 100,
      align: 'center' as const,
      render: (value: string) => {
        const config = CAMERA_SHOT_CONFIG[value as keyof typeof CAMERA_SHOT_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : value || '-'
      },
    },

    {
      title: 'B机状态',
      dataIndex: 'cameraStatusByB',
      key: 'cameraStatusByB',
      width: 100,
      align: 'center' as const,
      render: (value: number) => {
        const config = CAMERA_STATUS_CONFIG[value as keyof typeof CAMERA_STATUS_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : '-'
      },
    },
  
    {
      title: 'B机文件号',
      dataIndex: 'cameraFileByB',
      key: 'cameraFileByB',
      width: 100,
      align: 'center' as const,
      render: (value: string) => value || '-',
    },
    {
      title: '拍摄内容',
      dataIndex: 'content',
      key: 'content',
      align: 'center' as const,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 120,
      align: 'center' as const,
      ellipsis: true,
      render: (value: string) => value || '-',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      align: 'center' as const,
      render: (value: string) => value ? new Date(value).toLocaleString() : '-',
    },
  ]

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (!sceneData.length) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Empty description="暂无场记数据" />
      </div>
    )
  }

  // 构建 Collapse 面板数据
  const collapseItems = filteredSceneData.map((scene) => {
    const filmShotLogs = scene.filmShotLogs || []

    return {
      key: scene.id?.toString() || '',
      label: (
        <Space size="middle" style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space direction="vertical">
            <Space >
              <Typography.Text strong style={{ color: '#1890ff' }}>
                场次 {scene.sceneNumber}
              </Typography.Text>
              <Tag color={ATMOSPHERE_TYPE_CONFIG[scene.atmosphere as keyof typeof ATMOSPHERE_TYPE_CONFIG]?.color || 'default'}>
                {ATMOSPHERE_TYPE_CONFIG[scene.atmosphere as keyof typeof ATMOSPHERE_TYPE_CONFIG]?.label || scene.atmosphere}
              </Tag>
              <Tag color={LOCATION_TYPE_CONFIG[scene.locationType as keyof typeof LOCATION_TYPE_CONFIG]?.color || 'default'}>
                {LOCATION_TYPE_CONFIG[scene.locationType as keyof typeof LOCATION_TYPE_CONFIG]?.label || scene.locationType}
              </Tag>
            </Space>
            <Space style={{fontSize:14}} wrap>
              {scene.shootingLocation ? <Dict title="拍摄场景" value={scene.shootingLocation} /> : null}
              {scene.scene ? <Dict title="场景" value={scene.scene} /> : null}
              {scene.mainContent ? <Dict title="主要内容" value={scene.mainContent} /> : null}
              {scene.costumeMakeupTip ? <Dict title="服化道提示" value={scene.costumeMakeupTip} /> : null}
              {scene.groupExtraActors ? <Dict title="群演" value={scene.groupExtraActors} /> : null}
              {scene.specialActors ? <Dict title="特约/群特" value={scene.specialActors} /> : null}
              {scene.mainActors ? <Dict title="主演" value={scene.mainActors} /> : null}
              {scene.pageNumber ? <Dict title="页数" value={scene.pageNumber} /> : null}
            </Space>

          </Space>
          <Typography.Text type="secondary" style={{ fontSize: '12px' ,flexShrink:0,whiteSpace:'nowrap'}}>
            场记: {filmShotLogs.length} 条
          </Typography.Text>

        </Space>
      ),
      children: (filmShotLogs.length > 0 ? (
      
          <Table
            columns={filmShotLogColumns}
            dataSource={filmShotLogs}
            rowKey="id"
            pagination={false}
            scroll={{ x: 1320 }}
            size="small"
          />
      ) : (
        <Empty description="暂无场记" />
      )
      ),
    }
  })

  return (
    <div>
      <Space style={{ width: '100%', justifyContent: 'space-between', paddingBottom: 16 }}>
        <Typography.Title level={4} style={{ margin: 0 }}>
          场次与场记信息
        </Typography.Title>
        <Typography.Text type="secondary">
          {searchText.trim() ? (
            <>
              显示 {filteredSceneData.length} / {sceneData.length} 个场次，
              {filteredSceneData.reduce((total, scene) => total + (scene.filmShotLogs?.length || 0), 0)} /
              {sceneData.reduce((total, scene) => total + (scene.filmShotLogs?.length || 0), 0)} 条场记
            </>
          ) : (
            <>
              共 {sceneData.length} 个场次，{sceneData.reduce((total, scene) => total + (scene.filmShotLogs?.length || 0), 0)} 条场记
            </>
          )}
        </Typography.Text>
      </Space>

      <div style={{ marginBottom: 16 }}>
        <Input.Search
          placeholder="搜索场次编号、拍摄场景、场景、主要内容、演员等..."
          allowClear
          style={{ width: 400 }}
          onChange={(e) => onSearchDebounce(e.target.value)}
        />
      </div>

      {filteredSceneData.length === 0 && searchText.trim() ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
          <Empty description="未找到匹配的场次数据" />
        </div>
      ) : (
        <Collapse
          items={collapseItems}
          size="large"
        />
      )}
    </div>
  )
}

export default FilmShotLogTab
