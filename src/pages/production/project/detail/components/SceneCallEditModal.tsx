import { Button, Form, Input, InputNumber, Modal, Select, Space, TimePicker } from 'antd'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import React, { useEffect } from 'react'
import { MEAL_TYPE_OPTIONS } from '@/consts'
import dayjs from 'dayjs'
import { IPrSceneCall } from '../../store'

interface ISceneCallEditModalProps {
  visible: boolean
  sceneCall: IPrSceneCall | null
  onOk: (values: Partial<IPrSceneCall>) => Promise<void>
  onCancel: () => void
}

const SceneCallEditModal: React.FC<ISceneCallEditModalProps> = ({ visible, sceneCall, onOk, onCancel }) => {
  const [form] = Form.useForm()

  // 当弹窗打开时，设置表单初始值
  useEffect(() => {
    if (visible && sceneCall) {
      form.setFieldsValue({
        dayNumber: sceneCall.dayNumber || 1,
        scheduleRemark: sceneCall.scheduleRemark || '',
        remark: sceneCall.remark || '',
        responsibleDept: sceneCall.responsibleDept || '',
        contact: sceneCall.contact || '',
        meals:
          sceneCall.meals?.map(meal => ({
            mealType: meal.mealType,
            mealTime: meal.mealTime ? dayjs(meal.mealTime, 'HH:mm') : null,
            location: meal.location,
          })) || [],
      })
    } else if (visible && !sceneCall) {
      // 创建新通告单时的默认值
      form.setFieldsValue({
        dayNumber: 1,
        scheduleRemark: '',
        remark: '',
        responsibleDept: '',
        contact: '',
        meals: [],
      })
    }
  }, [visible, sceneCall, form])

  const handleOk = async () => {
    try {
      const values = await form.validateFields()

      // 处理 meals 数据格式转换
      const formattedValues = {
        ...values,
        meals:
          values.meals?.map((meal: any) => ({
            mealType: meal.mealType,
            mealTime: meal.mealTime ? meal.mealTime.format('HH:mm') : null,
            location: meal.location,
          })) || [],
      }

      await onOk(formattedValues)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal title="编辑通告单基本信息" open={visible} onOk={handleOk} onCancel={handleCancel} width={800}>
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          label="第几天"
          name="dayNumber"
          rules={[
            { required: true, message: '请输入第几天' },
            { type: 'number', min: 1, message: '天数必须大于0' },
          ]}
          hidden={!sceneCall}>
          <InputNumber placeholder="请输入第几天" style={{ width: '100%' }} min={1} />
        </Form.Item>

        <Form.Item label="行程安排" name="scheduleRemark">
          <Input.TextArea placeholder="请输入行程安排" rows={4} maxLength={500} showCount />
        </Form.Item>

        <Form.Item label="责任部门" name="responsibleDept">
          <Input.TextArea placeholder="请输入责任部门" maxLength={300} />
        </Form.Item>

        <Form.Item label="联系方式" name="contact">
          <Input.TextArea placeholder="请输入联系方式" maxLength={300} />
        </Form.Item>

        <Form.Item label="备注" name="remark">
          <Input.TextArea placeholder="请输入备注" rows={3} maxLength={300} showCount />
        </Form.Item>

        {/* 用餐安排 */}
        <Form.Item label="用餐安排">
          <Form.List name="meals">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                    <Form.Item
                      {...restField}
                      name={[name, 'mealType']}
                      rules={[{ required: true, message: '请选择用餐类型' }]}>
                      <Select placeholder="用餐类型" options={MEAL_TYPE_OPTIONS} style={{ width: 120 }} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'mealTime']}>
                      <TimePicker placeholder="用餐时间" format="HH:mm" style={{ width: 120 }} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'location']}>
                      <Input placeholder="用餐地点" style={{ width: 150 }} maxLength={100} />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    添加用餐
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default SceneCallEditModal
