.detailDrawer {
  .ant-drawer-body {
    padding: 16px;
  }
}

.drawerContent {
  height: calc(100vh - 120px);
  overflow: auto;
}

.basicInfoCard {
  margin-bottom: 16px;
}

.dayNumber {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 16px;
}

.scheduleRemark {
  background: #f6f8fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  margin-bottom: 12px;
}

.remarkContent {
  background: #fff7e6;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #faad14;
}

.responsibleDeptContent {
  background: #fff7e6;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #faad14;
}

.contactContent {
  background: #fff7e6;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #faad14;
}